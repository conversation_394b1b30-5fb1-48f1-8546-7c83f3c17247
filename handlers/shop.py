import logging, os
from datetime import datetime
from aiogram import Router, types, F
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, FSInputFile

# Conditional BSON import
try:
    from bson.objectid import ObjectId
    BSON_AVAILABLE = True
except ImportError:
    # Define dummy ObjectId class if BSON is not available
    class ObjectId:
        @staticmethod
        def is_valid(oid):
            return False
    BSON_AVAILABLE = False
from handlers.products import _get_product_id_from_string, send_catalog_message
from utils.local_file_handling import extract_image_from_object
from database.operations import (
    get_product,
    get_products_by_category,
    get_product_by_id,
    get_category_by_id,
)
from keyboards.shop_kb import (
    shop_main_keyboard,
    shop_categories_keyboard,
    product_detail_keyboard,
    no_products_keyboard,
)
from utils.telegram_helpers import safe_edit_message
from utils.button_layout import should_use_single_row
from utils.template_helpers import format_text
from utils.line_product_manager import LineProductTheme, line_product_manager

router = Router()
router.name = "shop_router"

logger = logging.getLogger(__name__)


@router.callback_query(F.data.startswith("out_of_stock:"))
async def handle_out_of_stock(callback_query: types.CallbackQuery):
    """Handle out of stock product interactions."""
    await callback_query.answer("❌ This product is out of stock for you!", show_alert=True)


@router.callback_query(F.data == "shop_main")
async def shop_main(callback_query: types.CallbackQuery):
    """Display the main shop page."""
    await callback_query.answer()

    keyboard = shop_main_keyboard()

    await safe_edit_message(
        callback_query.message,
        format_text("shop", "shop_welcome"),
        reply_markup=keyboard,
        parse_mode="HTML",
    )


@router.callback_query(lambda c: c.data == "shop_categories")
async def shop_categories(callback_query: types.CallbackQuery):
    """Display product categories."""
    await callback_query.answer()

    keyboard = shop_categories_keyboard()

    await safe_edit_message(
        callback_query.message,
        format_text("shop", "shop_categories"),
        reply_markup=keyboard,
        parse_mode="HTML",
    )


@router.callback_query(lambda c: c.data.startswith("shop_category:"))
async def category_products(callback_query: types.CallbackQuery):
    """Show products from a specific category."""
    await callback_query.answer()

    # Extract category ID
    category_id = callback_query.data.split(":")[1]

    # Get products from this category (automatically filters out purchased exclusive products)
    products = get_products_by_category(category_id)

    if not products:
        # No available products in this category
        await safe_edit_message(
            callback_query.message,
            format_text("shop", "empty_category"),
            reply_markup=no_products_keyboard(),
            parse_mode="HTML",
        )
        return

    # Create a keyboard with product buttons
    inline_keyboard = []

    for product in products:
        product_id = product.get("id") or product.get("_id")
        product_name = product.get("name", "Unknown Product")

        # Create the button with validated text
        button_text = f"{product_name}"
        button = InlineKeyboardButton(
            text=button_text, callback_data=f"select_product:{product_id}"
        )

        # Single-column layout - each product gets its own row
        inline_keyboard.append([button])

    # Add navigation buttons
    inline_keyboard.append(
        [
            InlineKeyboardButton(
                text="🔙 Back to Categories", callback_data="shop_categories"
            )
        ]
    )
    inline_keyboard.append(
        [InlineKeyboardButton(text="🏠 Main Menu", callback_data="return_to_main")]
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=inline_keyboard)

    # Create category products catalog text
    # Create category products catalog text
    catalog_text = "📂 <b>\u2022 CATEGORY PRODUCTS \u2022</b> 📂\n\n"
    catalog_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
    catalog_text += "<i>Our premium selection for you</i>\n"
    catalog_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"

    # Display products with details using centralized stock calculation
    separator = "┈┈┈┈┈┈┈ ⭒ ♢ ⭒ ┈┈┈┈┈┈┈\n"
    user_id = callback_query.from_user.id

    for i, product in enumerate(products, 1):
        name = product.get("name") or "Unknown Product"
        price = product.get("price") or 0
        description = product.get("description", "No description available")

        # Get only the first line, and limit to 50 characters if it's long
        first_line = description.split("\n", 1)[0]
        desc_short = first_line[:50] + ("..." if len(first_line) > 50 else "")

        # Determine product type emoji and status with user-specific availability
        if product.get("is_exclusive_single_use", False):
            product_emoji = "📄"
            status_text = " 🟢 <i>Available</i>"
        elif product.get("is_line_based", False):
            product_emoji = "📋"
            allow_shared_inventory = product.get("allow_shared_inventory", False)

            # Use centralized user-specific stock calculation for consistency with express checkout
            if allow_shared_inventory:
                from database.operations import get_available_lines_for_user
                # Use same product ID logic as express checkout for consistency
                product_id = product.get("_id") or product.get("id")
                total_lines = product.get("total_lines", 0)

                # Clear cache to ensure fresh data (consistent with express checkout)
                try:
                    from utils.line_product_manager import line_product_manager
                    line_product_manager.clear_validation_cache(product_id, user_id)
                except Exception as cache_clear_e:
                    logger.debug(f"Could not clear validation cache for product {product_id}, user {user_id}: {cache_clear_e}")

                # Get user-specific availability using the same method as express checkout
                user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
                user_available_count = len(user_available_lines)

                # Enhanced debugging for stock count investigation
                logger.info(f"🔍 CATEGORY LISTING STOCK DEBUG: User {user_id}, Product {product_id} ({product.get('name', 'Unknown')})")
                logger.info(f"   Total lines: {total_lines}")
                logger.info(f"   User available count: {user_available_count}")
                logger.info(f"   User available indices: {user_available_lines}")

                # Also get purchase history for debugging
                from database.operations import get_user_purchased_lines
                purchased_lines = get_user_purchased_lines(user_id, product_id)
                logger.info(f"   User purchased lines: {purchased_lines}")
                logger.info(f"   User purchased count: {len(purchased_lines)}")

                logger.debug(f"Shop display: User {user_id}, Product {product_id}, Available: {user_available_count}/{total_lines}")

                # Use the same status logic as express checkout for consistency
                if user_available_count == 0:
                    status_text = " 🔴 <i>You've purchased all available content</i>"
                elif user_available_count == total_lines:
                    status_text = f" 📊 <i>{user_available_count} new items for you</i>"
                else:
                    status_text = f" 📊 <i>{user_available_count} new items remaining</i>"
            else:
                # For exclusive inventory, use standard available lines
                available_lines = product.get("available_lines", 0)
                status_text = f" 📊 <i>{available_lines} items available</i>"
        else:
            product_emoji = "📦"
            status_text = ""

        catalog_text += f"{product_emoji} <b>{i}. {name}</b>{status_text}\n"
        catalog_text += f"💫 <i>{desc_short}</i>\n"
        catalog_text += f"💎 <b>Price:</b> <code>${price:.2f}</code>\n"

        # Add separator between products (except after the last one)
        if i < len(products):
            catalog_text += separator

    catalog_text += "\n<i>Tap any product below to view details</i> ✨"

    try:
        await safe_edit_message(
            callback_query.message,
            catalog_text,
            reply_markup=keyboard,
            parse_mode="HTML",
        )
    except Exception:
        # If editing fails, delete old message and send new one
        try:
            chat_id = callback_query.message.chat.id
            await callback_query.message.delete()
            await callback_query.bot.send_message(
                chat_id=chat_id,
                text=catalog_text,
                reply_markup=keyboard,
                parse_mode="HTML",
            )
        except Exception as inner_e:
            print(f"Error displaying category products: {inner_e}")


@router.callback_query(lambda c: c.data.startswith("select_product:"))
async def select_product(callback_query: types.CallbackQuery):
    """Display product details when a product is selected."""
    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    # Try to convert to appropriate ID type
    try:
        if ObjectId.is_valid(product_id):
            product_id = ObjectId(product_id)
        else:
            product_id = int(product_id)
    except (ValueError, ImportError):
        pass

    # Get product details
    product = get_product_by_id(product_id)

    if not product:
        await callback_query.message.edit_text(
            "❌ <b>\u2022 PRODUCT NOT FOUND \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<i>This product is no longer available in our collection.</i>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            "<i>Please explore our other premium offerings.</i>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="← Back to Shop", callback_data="shop_main"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    # Check if exclusive product is still available
    if product.get("is_exclusive_single_use", False):
        if product.get("is_purchased", False):
            await callback_query.message.edit_text(
                "🔴 <b>\u2022 EXCLUSIVE PRODUCT SOLD \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "📄 <i>This exclusive product has already been purchased and is no longer available.</i>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "🔒 <i>Exclusive products can only be sold once.</i>",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="← Back to Shop", callback_data="shop_main"
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            return

        # Check expiration date
        expiration_date = product.get("expiration_date")
        if expiration_date and datetime.now() > expiration_date:
            await callback_query.message.edit_text(
                "⏰ <b>\u2022 EXCLUSIVE PRODUCT EXPIRED \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "📄 <i>This exclusive product has expired and is no longer available for purchase.</i>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"🗓️ <i>Expired on: {expiration_date.strftime('%Y-%m-%d %H:%M')}</i>",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="← Back to Shop", callback_data="shop_main"
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            return

    # Get category information if available
    category_info = ""
    if product.get("category_id"):
        category = get_category_by_id(product.get("category_id"))
        if category:
            category_info = f"\u2022 <b>Category:</b> {category.get('name')}\n"

    # Format product details based on product type
    if product.get("is_exclusive_single_use", False):
        # Use exclusive product formatting
        try:
            from utils.exclusive_product_manager import exclusive_product_manager
            product_details = exclusive_product_manager.create_exclusive_product_display(product, context="shop")
        except ImportError:
            # Fallback formatting for exclusive products
            name = product.get("name") or "Unknown Product"
            description = product.get("description", "No description available")
            price = product.get("price") or 0

            product_details = (
                f"📄 <b>\u2022 EXCLUSIVE PRODUCT \u2022</b>\n\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                f"⭐ <b>{name}</b>\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                f"📝 <b>Description:</b>\n<i>{description}</i>\n\n"
                f"💰 <b>Price:</b> <code>${price:.2f}</code>\n"
                f"🟢 <b>Status:</b> Available\n\n"
                f"▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
                f"🔒 <i>This is an exclusive single-use product. Once purchased, it becomes permanently unavailable.</i>"
            )
    elif product.get("is_line_based", False):
        # Use line product formatting with user-specific stock
        from utils.line_product_manager import line_product_manager
        user_id = callback_query.from_user.id
        allow_shared_inventory = product.get("allow_shared_inventory", False)

        # Calculate user-specific availability once for consistency
        if allow_shared_inventory:
            from database.operations import get_available_lines_for_user
            # Use same product ID logic as express checkout for consistency
            product_id = product.get("_id") or product.get("id")
            total_lines = product.get("total_lines", 0)

            # Clear user-specific cache to ensure fresh stock counts after any recent purchases
            try:
                line_product_manager.clear_validation_cache(product_id, user_id)
                logger.debug(f"Cleared user-specific cache for shared inventory product {product_id} before display to user {user_id}")
            except Exception as cache_clear_e:
                logger.debug(f"Could not clear user-specific cache for product {product_id}, user {user_id}: {cache_clear_e}")

            # Get fresh user-specific availability
            user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
            available_lines = len(user_available_lines)

            # Enhanced debugging for stock count investigation
            logger.info(f"🔍 PRODUCT DETAILS STOCK DEBUG: User {user_id}, Product {product_id} ({product.get('name', 'Unknown')})")
            logger.info(f"   Total lines: {total_lines}")
            logger.info(f"   User available count: {available_lines}")
            logger.info(f"   User available indices: {user_available_lines}")

            # Also get purchase history for debugging
            from database.operations import get_user_purchased_lines
            purchased_lines = get_user_purchased_lines(user_id, product_id)
            logger.info(f"   User purchased lines: {purchased_lines}")
            logger.info(f"   User purchased count: {len(purchased_lines)}")

            logger.debug(f"Product details: User {user_id}, Product {product_id}, "
                        f"Available: {available_lines}/{total_lines}")

            # Use the new method with pre-calculated availability for consistency
            product_details = line_product_manager.create_line_product_display_with_availability(
                product, available_lines, allow_shared_inventory, context="shop", user_id=user_id
            )
        else:
            # For non-shared inventory, use the original method
            available_lines = product.get("available_lines", 0)
            product_details = line_product_manager.create_line_product_display_with_availability(
                product, available_lines, allow_shared_inventory, context="shop", user_id=user_id
            )
    else:
        # Use regular product formatting
        product_details = format_text(
            "shop",
            "product_details",
            product_name=product.get("name", "Unknown Product"),
            product_description=product.get("description", "No description available"),
            product_price=product.get("price", 0),
            category_info=category_info,
        )

    # Create purchase button based on product type and availability
    if product.get("is_exclusive_single_use", False):
        # Use exclusive product keyboard
        from keyboards.product_kb import exclusive_product_confirmation_keyboard
        keyboard = exclusive_product_confirmation_keyboard(product_id)
    elif product.get("is_line_based", False):
        # For line-based products, check user-specific availability
        allow_shared_inventory = product.get("allow_shared_inventory", False)
        if allow_shared_inventory:
            # Check if user has any available lines
            from database.operations import get_available_lines_for_user
            total_lines = product.get("total_lines", 0)
            user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
            user_available_count = len(user_available_lines)

            if user_available_count == 0:
                # User has no available lines - show out of stock keyboard
                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(
                        text="❌ You've purchased all available content",
                        callback_data=f"out_of_stock:{product_id}"
                    )],
                    [InlineKeyboardButton(
                        text="🔙 Back to Products",
                        callback_data="browse_products"
                    )]
                ])
            else:
                # User has available lines - show normal keyboard
                keyboard = product_detail_keyboard(product_id, product.get("price", 0))
        else:
            # For exclusive line-based products, check general availability
            available_lines = product.get("available_lines", 0)
            if available_lines == 0:
                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(
                        text="❌ Out of Stock",
                        callback_data=f"out_of_stock:{product_id}"
                    )],
                    [InlineKeyboardButton(
                        text="🔙 Back to Products",
                        callback_data="browse_products"
                    )]
                ])
            else:
                keyboard = product_detail_keyboard(product_id, product.get("price", 0))
    else:
        # Use regular product keyboard for non-line-based products
        keyboard = product_detail_keyboard(product_id, product.get("price", 0))

    # Get product image with explicit product context
    image_source = extract_image_from_object(product, object_type="product")
    has_valid_image = False

    # Check if we have a valid image
    if image_source:
        if image_source.startswith(("http://", "https://")):
            has_valid_image = True
        elif os.path.isfile(image_source):
            has_valid_image = True
        else:
            # Try to resolve the image path
            base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            possible_paths = [
                os.path.join(base_path, image_source),
                os.path.join(base_path, "uploads", image_source),
                os.path.join(
                    base_path,
                    "uploads",
                    "product_images",
                    os.path.basename(image_source),
                ),
            ]

            for path in possible_paths:
                if os.path.isfile(path):
                    image_source = path
                    has_valid_image = True
                    logger.debug(f"Found product image at: {path}")
                    break

    # Always delete the current message and create a new one for product views
    try:
        # Delete the current message
        await callback_query.message.delete()

        # If we have a valid product image, send it with the product details
        if has_valid_image:
            logger.info(f"Sending product with image: {image_source}")

            # Prepare photo argument
            if os.path.isfile(image_source):
                # Use FSInputFile for local files
                photo_arg = FSInputFile(path=image_source)
            else:
                # Use the image source directly (URL or file_id)
                photo_arg = image_source

            # Send the product with image
            await callback_query.bot.send_photo(
                chat_id=callback_query.message.chat.id,
                photo=photo_arg,
                caption=product_details,
                reply_markup=keyboard,
                parse_mode="HTML",
            )
        else:
            # No valid image, send text-only message
            await callback_query.bot.send_message(
                chat_id=callback_query.message.chat.id,
                text=product_details,
                reply_markup=keyboard,
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Error sending product details: {e}")
        # If there's an error, try to edit the message as fallback
        try:
            await safe_edit_message(
                callback_query.message,
                product_details,
                reply_markup=keyboard,
                parse_mode="HTML",
            )
        except Exception:
            # Last resort - try to send a new message
            try:
                await callback_query.bot.send_message(
                    chat_id=callback_query.message.chat.id,
                    text=product_details,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )
            except Exception:
                pass  # Nothing more we can do


@router.callback_query(lambda c: c.data.startswith("shop_view_product:"))
async def shop_view_product(callback_query: types.CallbackQuery):
    """Display detailed product information from the shop interface."""
    try:
        await callback_query.answer()  # Acknowledge click immediately

        # Extract product ID from callback data
        product_id_str = callback_query.data.split(":", 1)[1]
        product_id = _get_product_id_from_string(product_id_str)

        # Get the product details
        product = get_product(product_id)

        if not product:
            logger.warning(f"Shop view - Product not found: {product_id_str}")
            await safe_edit_message(
                callback_query.message,
                "❌ <b>Product Not Found</b> ❌\n\n"
                "<i>This product is no longer available or has been removed.</i>",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🛍️ Browse Products",
                                callback_data="browse_products",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="return_to_main"
                            )
                        ],
                    ]
                ),
                parse_mode="HTML",
            )
            return

        # Create product details text
        name = product.get("name") or "Premium Product"
        description = (
            product.get("description", "No details provided.") or "No details provided."
        )
        price = product.get("price") or 0
        # Get the actual ID used in DB for consistency in callbacks
        actual_product_id = product.get("_id") or product.get("id")

        product_details = (
            f"🛍️ <b>{name}</b> 🛍️\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>📝 Details:</b>\n{description}\n\n"
            f"<b>💎 Price:</b> <code>${price:.2f}</code>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<i>Ready to enhance your digital collection?</i>"
        )

        # Create keyboard with Buy and Add to Cart buttons
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="💰 Buy Now",
                        callback_data=f"buy_product:{actual_product_id}",
                    ),
                    InlineKeyboardButton(
                        text="🛒 Add to Cart",
                        callback_data=f"add_to_cart:{actual_product_id}",
                    ),
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Products", callback_data="browse_products"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🏠 Main Menu", callback_data="return_to_main"
                    )
                ],
            ]
        )

        # Get product image with explicit product context
        image_source = extract_image_from_object(product, object_type="product")
        logger.debug(f"Product image source for {name}: {image_source}")

        # Send the message with image if available
        is_photo = callback_query.message.photo is not None

        # Use the send_catalog_message for consistent handling
        await send_catalog_message(
            callback_query.bot,
            callback_query.message.chat.id,
            callback_query.message.message_id,
            product_details,
            keyboard,
            image_source=image_source,
            is_photo=is_photo,
            context="product",  # Explicitly set context as product to ensure correct image handling
        )

    except Exception as e:
        logger.exception(
            f"Error in shop_view_product for {callback_query.data}:", exc_info=e
        )
        # Provide user-friendly error message
        await safe_edit_message(
            callback_query.message,
            "❌ <b>Error Loading Product</b> ❌\n\n"
            "<i>We encountered an issue while loading this product.</i>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Again", callback_data=callback_query.data
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🛍️ Browse Products", callback_data="browse_products"
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )


def register_shop_handlers(dp):
    """Register all shop-related handlers."""
    # Register the router directly
    dp.include_router(router)
    logger.info("Shop handlers registered successfully")
